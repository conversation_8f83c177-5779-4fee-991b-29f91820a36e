package com.example

import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Prototype
import io.micronaut.core.convert.ConversionContext
import io.micronaut.core.convert.ConversionService
import io.micronaut.core.convert.TypeConverter
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import java.time.DateTimeException
import java.time.LocalDate
import java.util.Optional

@Controller("/foo")
class FooController(
    private val config2: Config2,
) {

    @Get
    fun index() {
        println(config2)
    }
}

@ConfigurationProperties("config.first")
class Config2 {
    lateinit var name: String
    lateinit var date: LocalDate
}

@Prototype
class MapToLocalDateConverter(
    private val conversionService: ConversionService, // (2)
) : TypeConverter<Map<*, *>, LocalDate> { // (1)

    override fun convert(
        propertyMap: Map<*, *>,
        targetType: Class<LocalDate>,
        context: ConversionContext,
    ): Optional<LocalDate> {
        val day = conversionService.convert(propertyMap["day"], Int::class.java)
        val month = conversionService.convert(propertyMap["month"], Int::class.java)
        val year = conversionService.convert(propertyMap["year"], Int::class.java)
        if (day.isPresent && month.isPresent && year.isPresent) {
            try {
                return Optional.of(LocalDate.of(year.get(), month.get(), day.get())) // (3)
            } catch (e: DateTimeException) {
                context.reject(propertyMap, e) // (4)
                return Optional.empty()
            }
        }

        return Optional.empty()
    }
}
