package com.example

import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Prototype
import io.micronaut.context.annotation.Value
import io.micronaut.core.convert.ConversionContext
import io.micronaut.core.convert.ConversionService
import io.micronaut.core.convert.TypeConverter
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import java.time.DateTimeException
import java.time.LocalDate
import java.util.Optional

@Controller("/foo")
class FooController(
    private val config2: Config2,
) {

    @Get
    fun index() {
        println("Config2: $config2")
        println("Name: ${config2.name}")
        println("Date: ${config2.date}")
    }
}

@ConfigurationProperties("config.first")
class Config2(
    private val conversionService: ConversionService
) {
    lateinit var name: String
    lateinit var date: LocalDate

    @ConfigurationInject
    fun setDate(@Property(name = "date") dateMap: Map<String, Any>) {
        println("Setting date from map: $dateMap")
        val day = conversionService.convert(dateMap["day"], Int::class.java).orElse(1)
        val month = conversionService.convert(dateMap["month"], Int::class.java).orElse(1)
        val year = conversionService.convert(dateMap["year"], Int::class.java).orElse(2024)
        println("Converted values - day: $day, month: $month, year: $year")
        this.date = LocalDate.of(year, month, day)
        println("Date set to: ${this.date}")
    }
}

@Prototype
class MapToLocalDateConverter(
    private val conversionService: ConversionService, // (2)
) : TypeConverter<Map<*, *>, LocalDate> { // (1)

    init {
        println("MapToLocalDateConverter initialized!")
    }

    override fun convert(
        propertyMap: Map<*, *>,
        targetType: Class<LocalDate>,
        context: ConversionContext,
    ): Optional<LocalDate> {
        println("MapToLocalDateConverter.convert called with: $propertyMap")
        val day = conversionService.convert(propertyMap["day"], Int::class.java)
        val month = conversionService.convert(propertyMap["month"], Int::class.java)
        val year = conversionService.convert(propertyMap["year"], Int::class.java)
        println("Converted values - day: $day, month: $month, year: $year")
        if (day.isPresent && month.isPresent && year.isPresent) {
            try {
                val result = LocalDate.of(year.get(), month.get(), day.get())
                println("Successfully created LocalDate: $result")
                return Optional.of(result) // (3)
            } catch (e: DateTimeException) {
                println("DateTimeException: ${e.message}")
                context.reject(propertyMap, e) // (4)
                return Optional.empty()
            }
        } else {
            println("Missing values - day: ${propertyMap["day"]}, month: ${propertyMap["month"]}, year: ${propertyMap["year"]}")
        }
        println("Returning empty Optional")
        return Optional.empty()
    }
}
